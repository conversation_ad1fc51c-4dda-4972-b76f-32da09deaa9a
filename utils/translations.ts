export type Language = 'tr' | 'en';

export interface Translations {
  appTitle: string;
  appSubtitle: string;
  addApiCodes: string;
  xtreamUI: string;
  addM3UUrl: string;
  playlistLink: string;
  extensions: string;
  extensionsPlugins: string;
  version: string;
  premiumEdition: string;
  language: string;
  turkish: string;
  english: string;
}

export const translations: Record<Language, Translations> = {
  tr: {
    appTitle: 'COO IPTV',
    appSubtitle: 'Premium Player',
    addApiCodes: 'API Kodları Ekle',
    xtreamUI: 'Xtream UI',
    addM3UUrl: 'M3U URL Ekle',
    playlistLink: 'Playlist Linki',
    extensions: 'Eklentiler',
    extensionsPlugins: 'Uzantılar & Eklentiler',
    version: 'Sürüm',
    premiumEdition: 'Premium Sürüm',
    language: 'Dil',
    turkish: 'Türkçe',
    english: 'İngilizce'
  },
  en: {
    appTitle: 'COO IPTV',
    appSubtitle: 'Premium Player',
    addApiCodes: 'Add API Codes',
    xtreamUI: 'Xtream UI',
    addM3UUrl: 'Add M3U URL',
    playlistLink: 'Playlist Link',
    extensions: 'Extensions',
    extensionsPlugins: 'Extensions & Plugins',
    version: 'Version',
    premiumEdition: 'Premium Edition',
    language: 'Language',
    turkish: 'Turkish',
    english: 'English'
  }
};

export const getTranslation = (language: Language): Translations => {
  return translations[language];
};
