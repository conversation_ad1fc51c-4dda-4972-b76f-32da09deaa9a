export type Language = 'tr' | 'en' | 'de' | 'ru';

export interface Translations {
  appTitle: string;
  appSubtitle: string;
  addApiCodes: string;
  xtreamUI: string;
  addM3UUrl: string;
  playlistLink: string;
  playSingleContent: string;
  singleContentDesc: string;
  extensions: string;
  extensionsPlugins: string;
  version: string;
  premiumEdition: string;
  language: string;
  turkish: string;
  english: string;
  german: string;
  russian: string;
}

export const translations: Record<Language, Translations> = {
  tr: {
    appTitle: 'COO IPTV',
    appSubtitle: 'Premium Player',
    addApiCodes: 'API Kodları Ekle',
    xtreamUI: 'Xtream UI',
    addM3UUrl: 'M3U URL Ekle',
    playlistLink: 'Playlist Linki',
    playSingleContent: 'Tek İçerik Oynat',
    singleContentDesc: 'Direkt Video/Stream',
    extensions: 'Eklentiler',
    extensionsPlugins: 'Uzantılar & Eklentiler',
    version: '<PERSON>ürüm',
    premiumEdition: 'Premium Sürüm',
    language: 'Dil',
    turkish: 'Türkçe',
    english: '<PERSON>ng<PERSON><PERSON><PERSON>',
    german: 'Almanca',
    russian: 'Rus<PERSON>'
  },
  en: {
    appTitle: 'COO IPTV',
    appSubtitle: 'Premium Player',
    addApiCodes: 'Add API Codes',
    xtreamUI: 'Xtream UI',
    addM3UUrl: 'Add M3U URL',
    playlistLink: 'Playlist Link',
    playSingleContent: 'Play Single Content',
    singleContentDesc: 'Direct Video/Stream',
    extensions: 'Extensions',
    extensionsPlugins: 'Extensions & Plugins',
    version: 'Version',
    premiumEdition: 'Premium Edition',
    language: 'Language',
    turkish: 'Turkish',
    english: 'English',
    german: 'German',
    russian: 'Russian'
  },
  de: {
    appTitle: 'COO IPTV',
    appSubtitle: 'Premium Player',
    addApiCodes: 'API-Codes hinzufügen',
    xtreamUI: 'Xtream UI',
    addM3UUrl: 'M3U-URL hinzufügen',
    playlistLink: 'Playlist-Link',
    playSingleContent: 'Einzelinhalt abspielen',
    singleContentDesc: 'Direktes Video/Stream',
    extensions: 'Erweiterungen',
    extensionsPlugins: 'Erweiterungen & Plugins',
    version: 'Version',
    premiumEdition: 'Premium Edition',
    language: 'Sprache',
    turkish: 'Türkisch',
    english: 'Englisch',
    german: 'Deutsch',
    russian: 'Russisch'
  },
  ru: {
    appTitle: 'COO IPTV',
    appSubtitle: 'Premium Player',
    addApiCodes: 'Добавить API коды',
    xtreamUI: 'Xtream UI',
    addM3UUrl: 'Добавить M3U URL',
    playlistLink: 'Ссылка плейлиста',
    playSingleContent: 'Воспроизвести контент',
    singleContentDesc: 'Прямое видео/поток',
    extensions: 'Расширения',
    extensionsPlugins: 'Расширения и плагины',
    version: 'Версия',
    premiumEdition: 'Премиум издание',
    language: 'Язык',
    turkish: 'Турецкий',
    english: 'Английский',
    german: 'Немецкий',
    russian: 'Русский'
  }
};

export const getTranslation = (language: Language): Translations => {
  return translations[language];
};
