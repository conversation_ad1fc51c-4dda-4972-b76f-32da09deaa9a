import { SafeAreaView, Text, TouchableOpacity, View } from "react-native";
import { Image } from "expo-image";

export default function Index() {
    const handleButtonPress = (buttonName: string) => {
        console.log(`${buttonName} butonuna basıldı`);
        // Burada ilgili sayfaya yönlendirme yapılabilir
    };

    return (
        <SafeAreaView className="flex-1 bg-gradient-to-br from-slate-900 to-slate-800">
            <View className="flex-1 justify-center items-center px-8">
                {/* Logo Bölümü */}
                <View className="mb-16">
                    <Image
                        source={require("../assets/images/react-logo.png")}
                        style={{ width: 120, height: 120 }}
                        contentFit="contain"
                        className="mb-4"
                    />
                    <Text className="text-white text-3xl font-bold text-center">
                        COO IPTV
                    </Text>
                    <Text className="text-slate-300 text-lg text-center mt-2">
                        Player
                    </Text>
                </View>

                {/* Buton<PERSON>ölümü */}
                <View className="w-full max-w-sm space-y-4">
                    {/* Canlı TV Butonu */}
                    <TouchableOpacity
                        onPress={() => handleButtonPress("Canlı TV")}
                        className="bg-blue-600 hover:bg-blue-700 active:bg-blue-800 rounded-xl py-4 px-6 shadow-lg"
                        activeOpacity={0.8}
                    >
                        <Text className="text-white text-xl font-semibold text-center">
                            📺 Canlı TV
                        </Text>
                    </TouchableOpacity>

                    {/* Film Butonu */}
                    <TouchableOpacity
                        onPress={() => handleButtonPress("Film")}
                        className="bg-purple-600 hover:bg-purple-700 active:bg-purple-800 rounded-xl py-4 px-6 shadow-lg"
                        activeOpacity={0.8}
                    >
                        <Text className="text-white text-xl font-semibold text-center">
                            🎬 Film & Dizi
                        </Text>
                    </TouchableOpacity>

                    {/* Ayarlar Butonu */}
                    <TouchableOpacity
                        onPress={() => handleButtonPress("Ayarlar")}
                        className="bg-gray-600 hover:bg-gray-700 active:bg-gray-800 rounded-xl py-4 px-6 shadow-lg"
                        activeOpacity={0.8}
                    >
                        <Text className="text-white text-xl font-semibold text-center">
                            ⚙️ Ayarlar
                        </Text>
                    </TouchableOpacity>
                </View>

                {/* Alt Bilgi */}
                <View className="mt-16">
                    <Text className="text-slate-400 text-sm text-center">
                        Sürüm 1.0.0
                    </Text>
                </View>
            </View>
        </SafeAreaView>
    );
}
