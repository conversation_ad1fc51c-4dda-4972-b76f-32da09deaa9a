import { SafeAreaView, Text, TouchableOpacity, View, ScrollView } from "react-native";
import { Image } from "expo-image";
import { LinearGradient } from "expo-linear-gradient";
import { useState } from "react";
import { Language, getTranslation } from "../utils/translations";

export default function Index() {
    const [currentLanguage, setCurrentLanguage] = useState<Language>('tr');
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const t = getTranslation(currentLanguage);

    const handleButtonPress = (buttonName: string) => {
        console.log(`${buttonName} butonuna basıldı`);
        // Burada ilgili sayfaya yönlendirme yapılabilir
    };

    const languages: { code: Language; name: string; flag: string }[] = [
        { code: 'tr', name: t.turkish, flag: '🇹🇷' },
        { code: 'en', name: t.english, flag: '🇺🇸' },
        { code: 'de', name: t.german, flag: '🇩🇪' },
        { code: 'ru', name: t.russian, flag: '🇷🇺' }
    ];

    const selectLanguage = (language: Language) => {
        setCurrentLanguage(language);
        setIsDropdownOpen(false);
    };

    const toggleDropdown = () => {
        setIsDropdownOpen(!isDropdownOpen);
    };

    const currentLang = languages.find(lang => lang.code === currentLanguage);

    return (
        <SafeAreaView className="flex-1">
            {/* Arka Plan Gradyanı */}
            <LinearGradient
                colors={['#0f0f23', '#1a1a2e', '#16213e']}
                className="flex-1"
            >


                <ScrollView
                    className="flex-1"
                    contentContainerStyle={{
                        flexGrow: 1,
                        justifyContent: 'center',
                        alignItems: 'center',
                        paddingHorizontal: 24,
                        paddingVertical: 20
                    }}
                    showsVerticalScrollIndicator={false}
                >
                    {/* Logo Bölümü */}
                    <View className="mb-20 items-center">
                        <View className="bg-white/10 rounded-full p-6 mb-6 shadow-2xl backdrop-blur-sm">
                            <Image
                                source={require("../assets/images/react-logo.png")}
                                style={{ width: 100, height: 100 }}
                                contentFit="contain"
                            />
                        </View>
                        <Text className="text-white text-4xl font-bold text-center mb-2">
                            {t.appTitle}
                        </Text>
                        <Text className="text-cyan-300 text-xl font-medium text-center">
                            {t.appSubtitle}
                        </Text>
                        <View className="w-20 h-1 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full mt-3"></View>
                    </View>

                    {/* Butonlar Bölümü */}
                    <View className="w-full max-w-sm">
                        {/* Xtream API Butonu */}
                        <View className="mb-6">
                            <TouchableOpacity
                                onPress={() => handleButtonPress("Xtream API")}
                                activeOpacity={0.8}
                                className="overflow-hidden rounded-2xl shadow-xl"
                            >
                                <LinearGradient
                                    colors={['#ff6b6b', '#ee5a52']}
                                    start={{ x: 0, y: 0 }}
                                    end={{ x: 1, y: 1 }}
                                    className="py-5 px-6"
                                >
                                    <View className="flex-row items-center justify-center">
                                        <Text className="text-3xl mr-3">🔗</Text>
                                        <View>
                                            <Text className="text-white text-xl font-bold">
                                                {t.addApiCodes}
                                            </Text>
                                            <Text className="text-white/80 text-sm">
                                                {t.xtreamUI}
                                            </Text>
                                        </View>
                                    </View>
                                </LinearGradient>
                            </TouchableOpacity>
                        </View>

                        {/* M3U URL Butonu */}
                        <View className="mb-6">
                            <TouchableOpacity
                                onPress={() => handleButtonPress("M3U URL")}
                                activeOpacity={0.8}
                                className="overflow-hidden rounded-2xl shadow-xl"
                            >
                                <LinearGradient
                                    colors={['#4ecdc4', '#44a08d']}
                                    start={{ x: 0, y: 0 }}
                                    end={{ x: 1, y: 1 }}
                                    className="py-5 px-6"
                                >
                                    <View className="flex-row items-center justify-center">
                                        <Text className="text-3xl mr-3">📋</Text>
                                        <View>
                                            <Text className="text-white text-xl font-bold">
                                                {t.addM3UUrl}
                                            </Text>
                                            <Text className="text-white/80 text-sm">
                                                {t.playlistLink}
                                            </Text>
                                        </View>
                                    </View>
                                </LinearGradient>
                            </TouchableOpacity>
                        </View>

                        {/* Eklentiler Butonu */}
                        <View className="mb-2">
                            <TouchableOpacity
                                onPress={() => handleButtonPress("Eklentiler")}
                                activeOpacity={0.8}
                                className="overflow-hidden rounded-2xl shadow-xl"
                            >
                                <LinearGradient
                                    colors={['#a855f7', '#8b5cf6']}
                                    start={{ x: 0, y: 0 }}
                                    end={{ x: 1, y: 1 }}
                                    className="py-5 px-6"
                                >
                                    <View className="flex-row items-center justify-center">
                                        <Text className="text-3xl mr-3">🧩</Text>
                                        <View>
                                            <Text className="text-white text-xl font-bold">
                                                {t.extensions}
                                            </Text>
                                            <Text className="text-white/80 text-sm">
                                                {t.extensionsPlugins}
                                            </Text>
                                        </View>
                                    </View>
                                </LinearGradient>
                            </TouchableOpacity>
                        </View>
                    </View>

                    {/* Footer - Dil Seçimi ve Sürüm Bilgisi */}
                    <View className="mt-16 items-center">
                        {/* Dil Dropdown */}
                        <View className="mb-4 relative">
                            <Text className="text-white text-sm font-medium text-center mb-3">
                                {t.language}
                            </Text>

                            {/* Dropdown Button */}
                            <TouchableOpacity
                                onPress={toggleDropdown}
                                activeOpacity={0.8}
                                className="bg-white/10 rounded-xl px-4 py-3 backdrop-blur-sm border border-white/20 min-w-[140px]"
                            >
                                <View className="flex-row items-center justify-between">
                                    <View className="flex-row items-center">
                                        <Text className="text-xl mr-2">{currentLang?.flag}</Text>
                                        <Text className="text-white font-medium">
                                            {currentLang?.name}
                                        </Text>
                                    </View>
                                    <Text className={`text-white text-lg transform ${isDropdownOpen ? 'rotate-180' : 'rotate-0'}`}>
                                        ▼
                                    </Text>
                                </View>
                            </TouchableOpacity>

                            {/* Dropdown Menu */}
                            {isDropdownOpen && (
                                <View
                                    className="absolute bottom-full left-0 right-0 mb-2 bg-slate-800/95 rounded-xl border border-white/20 backdrop-blur-sm shadow-2xl"
                                    style={{ zIndex: 1000, elevation: 1000 }}
                                >
                                    {[...languages].reverse().map((lang, index) => (
                                        <TouchableOpacity
                                            key={lang.code}
                                            onPress={() => selectLanguage(lang.code)}
                                            activeOpacity={0.8}
                                            className={`px-4 py-3 flex-row items-center ${
                                                index !== languages.length - 1 ? 'border-b border-white/10' : ''
                                            } ${
                                                currentLanguage === lang.code ? 'bg-cyan-500/20' : ''
                                            } ${
                                                index === 0 ? 'rounded-b-xl' : ''
                                            } ${
                                                index === languages.length - 1 ? 'rounded-t-xl' : ''
                                            }`}
                                        >
                                            <Text className="text-xl mr-3">{lang.flag}</Text>
                                            <Text
                                                className={`font-medium ${
                                                    currentLanguage === lang.code
                                                        ? 'text-cyan-300'
                                                        : 'text-white'
                                                }`}
                                            >
                                                {lang.name}
                                            </Text>
                                            {currentLanguage === lang.code && (
                                                <Text className="text-cyan-300 ml-auto">✓</Text>
                                            )}
                                        </TouchableOpacity>
                                    ))}
                                </View>
                            )}
                        </View>

                        {/* Sürüm Bilgisi */}
                        <View className="bg-white/5 rounded-full px-4 py-2 backdrop-blur-sm">
                            <Text className="text-cyan-300 text-sm font-medium">
                                {t.version} 1.0.0 • {t.premiumEdition}
                            </Text>
                        </View>
                    </View>
                </ScrollView>
            </LinearGradient>
        </SafeAreaView>
    );
}
