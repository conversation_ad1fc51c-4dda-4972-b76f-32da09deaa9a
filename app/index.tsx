import { SafeAreaView, Text, TouchableOpacity, View } from "react-native";
import { Image } from "expo-image";
import { LinearGradient } from "expo-linear-gradient";

export default function Index() {
    const handleButtonPress = (buttonName: string) => {
        console.log(`${buttonName} butonuna basıldı`);
        // Burada ilgili sayfaya yönlendirme yapılabilir
    };

    return (
        <SafeAreaView className="flex-1">
            {/* Arka Plan Gradyanı */}
            <LinearGradient
                colors={['#0f0f23', '#1a1a2e', '#16213e']}
                className="flex-1"
            >
                <View className="flex-1 justify-center items-center px-6">
                    {/* Logo Bölümü */}
                    <View className="mb-20 items-center">
                        <View className="bg-white/10 rounded-full p-6 mb-6 shadow-2xl backdrop-blur-sm">
                            <Image
                                source={require("../assets/images/react-logo.png")}
                                style={{ width: 100, height: 100 }}
                                contentFit="contain"
                            />
                        </View>
                        <Text className="text-white text-4xl font-bold text-center mb-2">
                            COO IPTV
                        </Text>
                        <Text className="text-cyan-300 text-xl font-medium text-center">
                            Premium Player
                        </Text>
                        <View className="w-20 h-1 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full mt-3"></View>
                    </View>

                    {/* Butonlar Bölümü */}
                    <View className="w-full max-w-sm space-y-5">
                        {/* Xtream API Butonu */}
                        <TouchableOpacity
                            onPress={() => handleButtonPress("Xtream API")}
                            activeOpacity={0.8}
                            className="overflow-hidden rounded-2xl shadow-xl"
                        >
                            <LinearGradient
                                colors={['#ff6b6b', '#ee5a52']}
                                start={{ x: 0, y: 0 }}
                                end={{ x: 1, y: 1 }}
                                className="py-5 px-6"
                            >
                                <View className="flex-row items-center justify-center">
                                    <Text className="text-3xl mr-3">🔗</Text>
                                    <View>
                                        <Text className="text-white text-xl font-bold">
                                            Add API Codes
                                        </Text>
                                        <Text className="text-white/80 text-sm">
                                            Xtream UI
                                        </Text>
                                    </View>
                                </View>
                            </LinearGradient>
                        </TouchableOpacity>

                        {/* M3U URL Butonu */}
                        <TouchableOpacity
                            onPress={() => handleButtonPress("M3U URL")}
                            activeOpacity={0.8}
                            className="overflow-hidden rounded-2xl shadow-xl"
                        >
                            <LinearGradient
                                colors={['#4ecdc4', '#44a08d']}
                                start={{ x: 0, y: 0 }}
                                end={{ x: 1, y: 1 }}
                                className="py-5 px-6"
                            >
                                <View className="flex-row items-center justify-center">
                                    <Text className="text-3xl mr-3">📋</Text>
                                    <View>
                                        <Text className="text-white text-xl font-bold">
                                            Add M3U URL
                                        </Text>
                                        <Text className="text-white/80 text-sm">
                                            Playlist Link
                                        </Text>
                                    </View>
                                </View>
                            </LinearGradient>
                        </TouchableOpacity>

                        {/* Eklentiler Butonu */}
                        <TouchableOpacity
                            onPress={() => handleButtonPress("Eklentiler")}
                            activeOpacity={0.8}
                            className="overflow-hidden rounded-2xl shadow-xl"
                        >
                            <LinearGradient
                                colors={['#a855f7', '#8b5cf6']}
                                start={{ x: 0, y: 0 }}
                                end={{ x: 1, y: 1 }}
                                className="py-5 px-6"
                            >
                                <View className="flex-row items-center justify-center">
                                    <Text className="text-3xl mr-3">🧩</Text>
                                    <View>
                                        <Text className="text-white text-xl font-bold">
                                            Eklentiler
                                        </Text>
                                        <Text className="text-white/80 text-sm">
                                            Extensions & Plugins
                                        </Text>
                                    </View>
                                </View>
                            </LinearGradient>
                        </TouchableOpacity>
                    </View>

                    {/* Alt Bilgi */}
                    <View className="mt-16 items-center">
                        <View className="bg-white/5 rounded-full px-4 py-2 backdrop-blur-sm">
                            <Text className="text-cyan-300 text-sm font-medium">
                                v1.0.0 • Premium Edition
                            </Text>
                        </View>
                    </View>
                </View>
            </LinearGradient>
        </SafeAreaView>
    );
}
